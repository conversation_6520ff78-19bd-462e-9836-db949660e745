
.mqtt-demo.data-v-e9e45546 {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.status-section.data-v-e9e45546 {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}
.status-card.data-v-e9e45546 {
  flex: 1;
  background: white;
  padding: 20rpx;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.status-label.data-v-e9e45546 {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.status-value.data-v-e9e45546 {
  font-size: 28rpx;
  font-weight: bold;
}
.connected.data-v-e9e45546 {
  color: #4CAF50;
}
.disconnected.data-v-e9e45546 {
  color: #f44336;
}
.control-section.data-v-e9e45546 {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}
.control-btn.data-v-e9e45546 {
  flex: 1;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}
.connect-btn.data-v-e9e45546 {
  background-color: #4CAF50;
  color: white;
}
.disconnect-btn.data-v-e9e45546 {
  background-color: #f44336;
  color: white;
}
.control-btn.data-v-e9e45546:disabled {
  background-color: #ccc;
  color: #999;
}
.message-section.data-v-e9e45546, .topic-section.data-v-e9e45546 {
  background: white;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}
.section-title.data-v-e9e45546 {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}
.input-group.data-v-e9e45546 {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}
.message-input.data-v-e9e45546, .topic-input.data-v-e9e45546 {
  flex: 1;
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
}
.send-btn.data-v-e9e45546, .subscribe-btn.data-v-e9e45546 {
  padding: 20rpx 30rpx;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
}
.send-btn.data-v-e9e45546:disabled, .subscribe-btn.data-v-e9e45546:disabled {
  background-color: #ccc;
}
.subscribed-topics.data-v-e9e45546 {
  margin-top: 20rpx;
}
.topic-title.data-v-e9e45546 {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.topic-item.data-v-e9e45546 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}
.topic-name.data-v-e9e45546 {
  font-size: 26rpx;
  color: #333;
}
.unsubscribe-btn.data-v-e9e45546 {
  padding: 10rpx 20rpx;
  background-color: #ff9800;
  color: white;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
}
.messages-section.data-v-e9e45546 {
  background: white;
  padding: 20rpx;
  border-radius: 10rpx;
}
.messages-container.data-v-e9e45546 {
  height: 400rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  padding: 10rpx;
  margin-bottom: 20rpx;
}
.message-item.data-v-e9e45546 {
  margin-bottom: 20rpx;
  padding: 15rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #ddd;
}
.message-item.sent.data-v-e9e45546 {
  background-color: #e3f2fd;
  border-left-color: #2196F3;
}
.message-item.received.data-v-e9e45546 {
  background-color: #f3e5f5;
  border-left-color: #9c27b0;
}
.message-item.system.data-v-e9e45546 {
  background-color: #fff3e0;
  border-left-color: #ff9800;
}
.message-item.error.data-v-e9e45546 {
  background-color: #ffebee;
  border-left-color: #f44336;
}
.message-header.data-v-e9e45546 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}
.message-time.data-v-e9e45546 {
  font-size: 24rpx;
  color: #999;
}
.message-topic.data-v-e9e45546 {
  font-size: 24rpx;
  color: #666;
  font-weight: bold;
}
.message-content.data-v-e9e45546 {
  font-size: 26rpx;
  color: #333;
  word-break: break-all;
}
.clear-btn.data-v-e9e45546 {
  width: 100%;
  padding: 20rpx;
  background-color: #607d8b;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
}

