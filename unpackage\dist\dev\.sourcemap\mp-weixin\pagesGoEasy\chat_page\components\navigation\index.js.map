{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/navigation/index.vue?4b85", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/navigation/index.vue?01f1", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/navigation/index.vue?f0c0", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/navigation/index.vue?fe69", "uni-app:///pagesGoEasy/chat_page/components/navigation/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/navigation/index.vue?a8dd", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/navigation/index.vue?b8d6"], "names": ["props", "title", "type", "default", "groupCount", "group_id", "isPrivate", "data", "<PERSON><PERSON><PERSON><PERSON>", "showTopping", "pageData", "watch", "computed", "customBar", "statusBar", "userInfo", "renderTextMessage", "renderTextMessageNo", "created", "methods", "to", "onMore", "member_id", "open", "close", "maskClick", "getData", "res", "noticeReaded", "uni", "API_notice", "http", "API_noticeReaded", "notice_id"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC8L;AAC9L,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAyuB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC6D7vB;AAGA;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;AACA;AAEA;AACA;AAAA,eAGA;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;EACA;EACAI;IACA;MAEAC;MAEAC;MACAC;IACA;EACA;EACAC;IACAN;MACA;QACA;UACA;QACA;MACA;IACA;EACA;EACAO;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;EACAC;EACAC;IACAC;IACAC;MACA;QACA;UACAC;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;kBACApB;kBACA;kBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAqB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACA5B;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA0B;gBACA;kBACA;kBACA;gBACA;gBACAE;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MACA;QACAC,UACA;UACA1B;QACA,GACA,MACA;UACA;UACA;QACA,EACA;MACA;IACA;IACA;IACA2B;MAAA;MACA;QACAD,UACA;UACA1B;UACA4B;QACA,GACA,MACA;UACA;UACA;QACA,EACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjNA;AAAA;AAAA;AAAA;AAAw5C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACA56C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesGoEasy/chat_page/components/navigation/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=55417510&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=55417510&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"55417510\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesGoEasy/chat_page/components/navigation/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=55417510&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"nowrap_ navigationBar-box\">\r\n\t\t\t<view :style=\"{ height: statusBar - 8 + 'px' }\"></view>\r\n\t\t\t<view class=\"flex_r fa_c fj_b nowrap_ navigationBar\" :style=\"{ height: customBar - statusBar - 8 + 'px' }\">\r\n\t\t\t\t<view class=\"icon_ navigationBar-icon\" @click=\"to()\">\r\n\t\t\t\t\t<image class=\"img\"\r\n\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyNC4yMTIgNTExLjgwNkw3ODcuODkgNzMuMDgzYzE2LjE5NC0xNi42MyAxNi4xOTQtNDMuOTc1IDAtNjAuNjA1LTE2LjE5NS0xNi42My00Mi40OTYtMTYuNjMtNTguNjE0IDBMMjM1Ljc1IDQ3OS4zNmMtOC42NDcgOC45Ny0xMi4zNDUgMjAuOTM1LTExLjcxOSAzMi40NDYtLjY0NSAxMS45MDggMy4wNzIgMjMuODc0IDExLjcyIDMyLjgyNGw0OTMuNTA2IDQ2Ni44ODNjMTYuMTE4IDE2LjY1IDQyLjQzOCAxNi42NSA1OC42MTQgMCAxNi4xOTQtMTcuMDg1IDE2LjE5NC00My45NzUgMC02MC42MDVMMzI0LjIxIDUxMS44MDYiIGZpbGw9IiMxZDFkMWQiIGRhdGEtc3BtLWFuY2hvci1pZD0iYTMxM3guc2VhcmNoX2luZGV4LjAuaTguMmM2YTNhODFJY0NLRlYiIGNsYXNzPSJzZWxlY3RlZCIvPjwvc3ZnPg==\"\r\n\t\t\t\t\t\tmode=\"aspectFill\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex1 icon_ nowrap_ navigationBar-text bold_\">\r\n\t\t\t\t\t<text class=\"nowrap_\">\r\n\t\t\t\t\t\t{{ title }}\r\n\t\t\t\t\t\t<text v-if=\"groupCount\">（{{ groupCount }}）</text>\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"navigationBar-icon\" @click=\"onMore\">\r\n\t\t\t\t\t<image class=\"img\"\r\n\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTcuMjI0OTk5OTk5OTk5OTk0IDUwNy4wMzNhMTE3LjIzIDExNy4yMyAwIDEgMCAyMzQuNDYgMCAxMTcuMjMgMTE3LjIzIDAgMSAwLTIzNC40NiAwek0zOTYuNjY3MDAwMDAwMDAwMDMgNTA3LjAzM2ExMTcuMjMgMTE3LjIzIDAgMSAwIDIzNC40NiAwIDExNy4yMyAxMTcuMjMgMCAxIDAtMjM0LjQ2IDB6TTc4NS45MjggNTA3LjAzM2ExMTcuMjMgMTE3LjIzIDAgMSAwIDIzNC40NiAwIDExNy4yMyAxMTcuMjMgMCAxIDAtMjM0LjQ2IDB6Ii8+PC9zdmc+\"\r\n\t\t\t\t\t\tmode=\"aspectFill\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t<view class=\"wx-srt\" :style=\"{ width: barWidth + 'px' }\"></view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 置顶信息 -->\r\n\t\t\t<view class=\"icon_ topping\" :class=\"{ topping_: showTopping }\" @click=\"open\">\r\n\t\t\t\t<view class=\"topping-icon\">\r\n\t\t\t\t\t<image class=\"img\"\r\n\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTU5Ny42MjkgNzczLjMxNWgyMC4xNXYzLjQ3NmgtMjAuMTV2LTMuNDc2em04OC42NTQtMjEuNTA5aDQuOTg2djE2LjExOGgtNC45ODZ2LTE2LjExN3ptMTUuNzEzLS4xaDUuMDM4djE2LjIxOGgtNS4wMzh2LTE2LjIxOHptLTEwNC4zNjcgMjguMjA4aDIwLjE1djMuNDc1aC0yMC4xNXYtMy40NzV6bS00MzcuNTUtMzYwLjIyNmg3MDEuODM1djQ4Mi41M0gxNjAuMDh2LTQ4Mi41M3ptNzY3LjYzNyA1NTQuNzY1SDk0LjI4NFYzNTIuMjI2aDgzMy40MzJ2NjIyLjIyN3ptLTgwNy43NTUtMjUuNjg1aDc4Mi4wNDJWMzc3LjkwNEgxMTkuOTZ2NTcwLjg2NHptNjc1LjQ3LTYxMy43ODlMNTUwLjgwNyAxMzkuMjlsMTguOTg0LTIzLjcxNSAyNzQuNzcgMjE5LjQwNXptLTU2OC44NzggMGwyNDQuNjM1LTE5NS42OS0xOC45Ny0yMy43MTUtMjc0Ljc5MiAyMTkuNDA1ek01NTQuODQ2IDgzLjU1M2MwIDI0LjI0Ny0xOS42MzMgNDMuODc0LTQzLjg1IDQzLjg3NC0yNC4yMzEgMC00My44OC0xOS42MjctNDMuODgtNDMuODc0IDAtMjQuMjE1IDE5LjY0OS00My44NjUgNDMuODgtNDMuODY1IDI0LjIxNyAwIDQzLjg1IDE5LjY1IDQzLjg1IDQzLjg2NXoiIGZpbGw9IiNmZmMzMDEiLz48L3N2Zz4=\"\r\n\t\t\t\t\t\tmode=\"aspectFill\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"text_28 nowrap_ flex1 topping-text\">\r\n\t\t\t\t\t<view class=\"nowrap_ text_32\" v-html=\"renderTextMessageNo\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<uni-popup ref=\"popup\" type=\"top\" maskBackgroundColor=\"rgba(255, 255, 255, 0.7)\" @maskClick=\"maskClick\">\r\n\t\t\t<view class=\"flex_c popup-box\">\r\n\t\t\t\t<view class=\"popup-top\" :style=\"{ height: $store.state.StatusBar.customBar + 10 + 'px' }\"></view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<view class=\"text_32\" :style=\"{ whiteSpace: 'pre-wrap' }\" v-html=\"renderTextMessage\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex_r popup-box-bottom\">\r\n\t\t\t\t\t<view class=\"icon_ text_30 bold_ size_white button\" @click=\"noticeReaded\">\r\n\t\t\t\t\t\t<view class=\"button-icon\">\r\n\t\t\t\t\t\t\t<image class=\"img\"\r\n\t\t\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTgzMi44NTMgODY2Ljk4N2gtNjE0LjRjLTU4LjAyNiAwLTEwMi40LTQ0LjM3NC0xMDIuNC0xMDIuNFY0MzAuMDhjMC0zNy41NDcgMjAuNDgtNzEuNjggNTQuNjE0LTg4Ljc0N2wzMDcuMi0xNzAuNjY2YzMwLjcyLTE3LjA2NyA2NC44NTMtMTcuMDY3IDk1LjU3MyAwbDMyMC44NTMgMTYwLjQyNmMyMy44OTQgMTAuMjQgMzcuNTQ3IDM0LjEzNCAzNy41NDcgNTguMDI3cy0xMy42NTMgNDcuNzg3LTM0LjEzMyA2MS40NGwtMzE3LjQ0IDE4NC4zMmMtMzQuMTM0IDIwLjQ4LTc4LjUwNyAxNy4wNjctMTEyLjY0LTMuNDEzTDI2Ni4yNCA0ODguMTA3Yy0xMy42NTMtMTAuMjQtMjAuNDgtMzAuNzItNi44MjctNDcuNzg3czMwLjcyLTIwLjQ4IDQ3Ljc4Ny02LjgyN2wyMDEuMzg3IDE0My4zNmMxMC4yNCA2LjgyNyAyMy44OTMgNi44MjcgMzcuNTQ2IDMuNDE0bDMxNy40NC0xODQuMzJMNTQyLjcyIDIzNS41MmMtMTAuMjQtMy40MTMtMjAuNDgtMy40MTMtMzAuNzIgMEwyMDQuOCA0MDIuNzczYy0xMC4yNCA2LjgyNy0xNy4wNjcgMTcuMDY3LTE3LjA2NyAzMC43MlY3NjhjMCAyMC40OCAxMy42NTQgMzQuMTMzIDM0LjEzNCAzNC4xMzNoNjE0LjRjMjAuNDggMCAzNC4xMzMtMTMuNjUzIDM0LjEzMy0zNC4xMzNWNTYzLjJjMC0yMC40OCAxMy42NTMtMzQuMTMzIDM0LjEzMy0zNC4xMzNzMzQuMTM0IDEzLjY1MyAzNC4xMzQgMzQuMTMzVjc2OGMtMy40MTQgNTEuMi00Ny43ODcgOTguOTg3LTEwNS44MTQgOTguOTg3eiIgZmlsbD0iI2ZmZiIgZGF0YS1zcG0tYW5jaG9yLWlkPSJhMzEzeC5zZWFyY2hfaW5kZXguMC5pNS42NTgyM2E4MWJhWkhnMiIgY2xhc3M9InNlbGVjdGVkIi8+PC9zdmc+\"\r\n\t\t\t\t\t\t\t\tmode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t已读\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState\r\n\t} from 'vuex';\r\n\timport {\r\n\t\tto,\r\n\t\tshow\r\n\t} from '@/utils/index.js';\r\n\timport {\r\n\t\tEmojiDecoder,\r\n\t\temojiMap\r\n\t} from '../../../lib/EmojiDecoder.js';\r\n\tconst emojiUrl = 'https://imgcache.qq.com/open/qcloud/tim/assets/emoji/';\r\n\tconst decoder = new EmojiDecoder(emojiUrl, emojiMap);\r\n\t// #ifdef MP\r\n\tconst menuButtonInfo = wx.getMenuButtonBoundingClientRect();\r\n\tlet barWidth = menuButtonInfo.width;\r\n\t// #endif\r\n\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tgroupCount: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tgroup_id: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tisPrivate: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tbarWidth,\r\n\t\t\t\t// #endif\r\n\t\t\t\tshowTopping: false,\r\n\t\t\t\tpageData: {}\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tgroup_id(value) {\r\n\t\t\t\tif (value) {\r\n\t\t\t\t\tif (!this.isPrivate) {\r\n\t\t\t\t\t\tthis.getData();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tcustomBar: (state) => state.StatusBar.customBar,\r\n\t\t\tstatusBar: (state) => state.StatusBar.statusBar,\r\n\t\t\tuserInfo: (state) => state.userInfo,\r\n\t\t\t//渲染文本消息，如果包含表情，替换为图片\r\n\t\t\t//todo:本不需要该方法，可以在标签里完成，但小程序有兼容性问题，被迫这样实现\r\n\t\t\trenderTextMessage() {\r\n\t\t\t\tconst text = this.pageData.content;\r\n\t\t\t\tif (!text) return '';\r\n\t\t\t\treturn '<span>' + decoder.decode(text) + '</span>';\r\n\t\t\t},\r\n\t\t\trenderTextMessageNo() {\r\n\t\t\t\tconst text = this.pageData.content;\r\n\t\t\t\tif (!text) return '';\r\n\t\t\t\treturn '<span>' + decoder.decodeNo(text) + '</span>';\r\n\t\t\t}\r\n\t\t}),\r\n\t\tcreated() {},\r\n\t\tmethods: {\r\n\t\t\tto,\r\n\t\t\tonMore() {\r\n\t\t\t\tif (this.isPrivate) {\r\n\t\t\t\t\tto(`/pagesGoEasy/group_member_infor/index`, {\r\n\t\t\t\t\t\tmember_id: this.group_id\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tto(`/pagesGoEasy/group_infor/index?group_id=${this.group_id}`);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\topen() {\r\n\t\t\t\tthis.showTopping = false;\r\n\t\t\t\tthis.$refs.popup.open();\r\n\t\t\t},\r\n\t\t\tclose() {\r\n\t\t\t\tthis.showTopping = false;\r\n\t\t\t\tthis.$refs.popup.close();\r\n\t\t\t},\r\n\t\t\tmaskClick() {\r\n\t\t\t\tthis.showTopping = true;\r\n\t\t\t},\r\n\t\t\tasync getData() {\r\n\t\t\t\tconst res = await this.API_notice();\r\n\t\t\t\tif (res) {\r\n\t\t\t\t\tconst data = res.data.data;\r\n\t\t\t\t\tthis.pageData = data;\r\n\t\t\t\t\tif (!data.is_new) {\r\n\t\t\t\t\t\tthis.showTopping = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!data.content) this.showTopping = false;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync noticeReaded() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t});\r\n\t\t\t\tconst res = await this.API_noticeReaded();\r\n\t\t\t\tif (res) {\r\n\t\t\t\t\tthis.close();\r\n\t\t\t\t\tshow('标记已读', 2000, 'success');\r\n\t\t\t\t}\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t},\r\n\t\t\tAPI_notice() {\r\n\t\t\t\treturn new Promise((res) => {\r\n\t\t\t\t\thttp.post(\r\n\t\t\t\t\t\t'Group/notice', {\r\n\t\t\t\t\t\t\tgroup_id: this.group_id\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\ttrue,\r\n\t\t\t\t\t\t(r) => {\r\n\t\t\t\t\t\t\tif (r.data.code == 0) return res(r);\r\n\t\t\t\t\t\t\treturn show(r.data.msg), res(false);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 已读\r\n\t\t\tAPI_noticeReaded() {\r\n\t\t\t\treturn new Promise((res) => {\r\n\t\t\t\t\thttp.post(\r\n\t\t\t\t\t\t'Group/noticeReaded', {\r\n\t\t\t\t\t\t\tgroup_id: this.group_id,\r\n\t\t\t\t\t\t\tnotice_id: this.pageData.notice_id\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\ttrue,\r\n\t\t\t\t\t\t(r) => {\r\n\t\t\t\t\t\t\tif (r.data.code == 0) return res(r);\r\n\t\t\t\t\t\t\treturn show(r.data.msg), res(false);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t);\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.topping {\r\n\t\tposition: relative;\r\n\t\ttop: -10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tpadding: 0 20rpx;\r\n\t\twidth: calc(100% - 40rpx);\r\n\t\theight: 0rpx;\r\n\t\tmargin: 0rpx auto;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-radius: 10rpx;\r\n\t\tbackground-color: #fff;\r\n\t\toverflow: hidden;\r\n\t\topacity: 0;\r\n\t\ttransition: all 0.3s;\r\n\r\n\t\t.topping-icon {\r\n\t\t\twidth: 50rpx;\r\n\t\t\theight: 50rpx;\r\n\t\t\tmargin-bottom: 4rpx;\r\n\t\t\tmargin-right: 16rpx;\r\n\t\t}\r\n\r\n\t\t.topping-text {}\r\n\t}\r\n\r\n\t.topping_ {\r\n\t\tposition: relative;\r\n\t\ttop: 0rpx;\r\n\t\theight: 80rpx;\r\n\t\tmargin: 20rpx auto;\r\n\t\topacity: 1;\r\n\t}\r\n\r\n\t.wx-srt {\r\n\t\theight: 20rpx;\r\n\t}\r\n\r\n\t.popup-box {\r\n\t\tbox-sizing: border-box;\r\n\t\tpadding: 0 20rpx 20rpx 20rpx;\r\n\t\twidth: 100%;\r\n\t\tmax-height: 70vh;\r\n\t\tborder-radius: 0 0 20rpx 20rpx;\r\n\t\tbackground-color: #fff;\r\n\r\n\t\t.content {\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\twidth: 100%;\r\n\t\t\toverflow-x: auto;\r\n\t\t}\r\n\r\n\t\t.popup-box-bottom {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 60rpx;\r\n\t\t\tmargin-top: 10rpx;\r\n\t\t\tflex-direction: row-reverse;\r\n\r\n\t\t\t.button {\r\n\t\t\t\twidth: 150rpx;\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\tbackground-color: #05c160;\r\n\r\n\t\t\t\t.button-icon {\r\n\t\t\t\t\twidth: 44rpx;\r\n\t\t\t\t\theight: 44rpx;\r\n\t\t\t\t\tmargin-bottom: 4rpx;\r\n\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.navigationBar-box {\r\n\t\tposition: fixed;\r\n\t\tz-index: 98;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\tpadding-bottom: 8px;\r\n\t\tbackground-color: rgba(237, 237, 237, 0.9);\r\n\t\tbackdrop-filter: blur(10px);\r\n\r\n\t\t.navigationBar {\r\n\t\t\twidth: 100%;\r\n\r\n\t\t\t.navigationBar-icon {\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tmargin: 0 30rpx;\r\n\r\n\t\t\t\t.img {\r\n\t\t\t\t\twidth: 90%;\r\n\t\t\t\t\theight: 90%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.navigationBar-text {\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tmargin: 0 20rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=55417510&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=55417510&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754970078164\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}