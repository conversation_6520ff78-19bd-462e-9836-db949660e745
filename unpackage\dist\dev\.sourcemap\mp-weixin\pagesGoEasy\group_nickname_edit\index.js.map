{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_nickname_edit/index.vue?5ea4", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_nickname_edit/index.vue?9323", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_nickname_edit/index.vue?5c21", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_nickname_edit/index.vue?f7f7", "uni-app:///pagesGoEasy/group_nickname_edit/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_nickname_edit/index.vue?b123", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_nickname_edit/index.vue?efb1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "avatar", "name", "onLoad", "methods", "confirm", "uni"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACwL;AACxL,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACbA;AAAA;AAAA;AAAA;AAA2sB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACyB/tB;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eACA;EACAC;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;EACA;;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACAC;gBACAA;gBAAA;gBAAA,OACA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvDA;AAAA;AAAA;AAAA;AAAk2C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAt3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesGoEasy/group_nickname_edit/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesGoEasy/group_nickname_edit/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=9459d982&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=9459d982&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9459d982\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesGoEasy/group_nickname_edit/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=9459d982&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.name = \"\"\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view style=\"height: 130rpx\"></view>\n\t\t<view class=\"text_40 bold_ title\">我在群里的昵称</view>\n\t\t<view class=\"text_30 text\">昵称昵称后，只会在此群显示，群内成员都可见。</view>\n\t\t<view class=\"icon_ row\">\n\t\t\t<view class=\"row-img\">\n\t\t\t\t<image class=\"img\" :src=\"avatar\" mode=\"aspectFill\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"flex1 row-input\">\n\t\t\t\t<input v-model=\"name\" confirm-type=\"done\" focus hold-keyboard :adjust-position=\"false\" type=\"text\" />\n\t\t\t</view>\n\t\t\t<view class=\"row-icon\" @click=\"name = ''\">\n\t\t\t\t<image\n\t\t\t\t\tclass=\"img\"\n\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxMi4yIDY0LjRjLTI0Ny40IDAtNDQ3LjkgMjAwLjYtNDQ3LjkgNDQ4czIwMC41IDQ0Ny45IDQ0Ny45IDQ0Ny45IDQ0Ny45LTIwMC41IDQ0Ny45LTQ0Ny45LTIwMC41LTQ0OC00NDcuOS00NDh6bTIwMy41IDYwNS45YzEyLjUgMTIuNSAxMi41IDMzLjEgMCA0NS42cy0zMy4xIDEyLjUtNDUuNiAwbC0xNTgtMTU4LTE1OCAxNThjLTEyLjUgMTIuNS0zMy4xIDEyLjUtNDUuNiAwcy0xMi41LTMzLjEgMC00NS42bDE1OC0xNTgtMTU4LTE1OGMtMTIuNS0xMi41LTEyLjUtMzMuMSAwLTQ1LjZzMzMuMS0xMi41IDQ1LjYgMGwxNTggMTU4IDE1OC0xNThjMTIuNS0xMi41IDMzLjEtMTIuNSA0NS42IDBzMTIuNSAzMy4xIDAgNDUuNmwtMTU4IDE1OCAxNTggMTU4eiIgZmlsbD0iIzcwNzA3MCIvPjwvc3ZnPg==\"\n\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t></image>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"icon_ text_30 bold_ size_white confirm\" @click=\"confirm\">确定</view>\n\t</view>\n</template>\n\n<script>\nimport { show, to, jsonUrl } from '@/utils/index.js';\nlet group_id = null;\nexport default {\n\tcomponents: {},\n\tdata() {\n\t\treturn {\n\t\t\tavatar: '',\n\t\t\tname: ''\n\t\t};\n\t},\n\tonLoad(e) {\n\t\tconst data = jsonUrl(e);\r\n\t\t// console.log(data)\r\n\t\t// group_id = data.group_id;\n\t\t// this.name = data.nickname;\n\t\t// this.avatar = data.member_avatar;\n\t},\n\tmethods: {\n\t\t// 提交\n\t\tasync confirm() {\n\t\t\tif (!this.name) return show('昵称不能为空');\n\n\t\t\t\t// 更新上一个页面\n\t\t\t\tuni.$emit('getGroupMemberInfo');\n\t\t\t\tuni.$off('getGroupMemberInfo');\n\t\t\t\tawait show('提交成功', 1500, 'success');\n\t\t\t\tto();\n\t\t\t\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.title {\n\twidth: 100%;\n\theight: 80rpx;\n\ttext-align: center;\n\tmargin: 0 auto;\n}\n.text {\n\twidth: 100%;\n\theight: 60rpx;\n\ttext-align: center;\n\tmargin: 0 auto;\n}\n.row {\n\twidth: calc(100% - 60rpx);\n\theight: 110rpx;\n\tmargin: 30rpx auto 200rpx auto;\n\tborder-top: 0.5px solid rgba(153, 153, 153, 0.3);\n\tborder-bottom: 0.5px solid rgba(153, 153, 153, 0.3);\n\t.row-img {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tmargin-left: 10rpx;\n\t\tborder-radius: 10rpx;\n\t\toverflow: hidden;\n\t\tbackground-color: #f1f1f1;\n\t}\n\t.row-input {\n\t\theight: 80rpx;\n\t\tmargin: 0 20rpx;\n\t\tinput {\n\t\t\theight: 100%;\n\t\t\tline-height: 80rpx;\n\t\t}\n\t}\n\t.row-icon {\n\t\twidth: 38rpx;\n\t\theight: 38rpx;\n\t\tmargin-right: 10rpx;\n\t}\n}\n.confirm {\n\twidth: 300rpx;\n\theight: 80rpx;\n\tborder-radius: 10rpx;\n\tmargin: 0 auto;\n\tbackground-color: #05c160;\n}\n</style>\n", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=9459d982&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=9459d982&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754970078134\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}