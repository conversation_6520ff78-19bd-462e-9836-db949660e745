{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/item/m-functional-module.vue?0eac", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/item/m-functional-module.vue?da5c", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/item/m-functional-module.vue?0a96", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/item/m-functional-module.vue?98d5", "uni-app:///pagesGoEasy/chat_page/components/item/m-functional-module.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/item/m-functional-module.vue?8601", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/item/m-functional-module.vue?13a8"], "names": ["props", "isMy", "type", "default", "value", "data", "computed", "methods", "onClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4I;AAC5I;AACuE;AACL;AACsC;;;AAGxG;AAC8L;AAC9L,gBAAgB,yLAAU;AAC1B,EAAE,yFAAM;AACR,EAAE,0GAAM;AACR,EAAE,mHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAuvB,CAAgB,yrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC6B3wB;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;EACA;EACAC;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAAs6C,CAAgB,gvCAAG,EAAC,C;;;;;;;;;;;ACA17C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesGoEasy/chat_page/components/item/m-functional-module.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./m-functional-module.vue?vue&type=template&id=355e74fb&scoped=true&\"\nvar renderjs\nimport script from \"./m-functional-module.vue?vue&type=script&lang=js&\"\nexport * from \"./m-functional-module.vue?vue&type=script&lang=js&\"\nimport style0 from \"./m-functional-module.vue?vue&type=style&index=0&id=355e74fb&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"355e74fb\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesGoEasy/chat_page/components/item/m-functional-module.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-functional-module.vue?vue&type=template&id=355e74fb&scoped=true&\"", "var components\ntry {\n  components = {\n    mLine: function () {\n      return import(\n        /* webpackChunkName: \"components/m-line/m-line\" */ \"@/components/m-line/m-line.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-functional-module.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-functional-module.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"flex_c row\">\n\t\t<view class=\"flex_r text-box\" :class=\"{ text_box: isMy }\" @tap.stop=\"onClick\">\n\t\t\t<view class=\"text\" :class=\"isMy ? 'text_r' : 'text_l'\">\n\t\t\t\t<view class=\"flex_c_c article\">\n\t\t\t\t\t<view class=\"flex_r fa_c article-info\">\n\t\t\t\t\t\t<view class=\"article-img\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"value.payload.image\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"flex1\">\n\t\t\t\t\t\t\t<view class=\"text_32 article-title\">{{ value.payload.title }}</view>\n\t\t\t\t\t\t\t<view class=\"text_26 color__ article-title\">{{ value.payload.text }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"m-line\">\n\t\t\t\t\t\t<m-line color=\"#f0f0f0\" length=\"100%\" :hairline=\"true\"></m-line>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex_r fa_c article-b\">\n\t\t\t\t\t\t<!-- <view class=\"article-b-icon\"></view> -->\n\t\t\t\t\t\t<view class=\"text_20 color__ article-b-text\">功能分享</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tprops: {\n\t\tisMy: {\n\t\t\ttype: [Boolean, Number],\n\t\t\tdefault: false\n\t\t},\n\t\tvalue: {\n\t\t\ttype: Object,\n\t\t\tdefault: {}\n\t\t}\n\t},\n\tdata() {\n\t\treturn {};\n\t},\n\tcomputed: {},\n\tmethods: {\n\t\tonClick() {\n\t\t\tthis.$emit('onClick');\n\t\t}\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.row {\n}\n.row_ {\n\tflex-direction: row-reverse;\n}\n.text_box {\n\tflex-direction: row-reverse;\n}\n.text {\n\tposition: relative;\n\tz-index: 99;\n\tbox-sizing: border-box;\n}\n\n.text_r {\n\tposition: relative;\n}\n.text_l {\n\tposition: relative;\n}\n\n.text_r::after {\n\tposition: absolute;\n\tz-index: -1;\n\tcontent: '';\n\ttop: 26rpx;\n\tright: -8rpx;\n\twidth: 18rpx;\n\theight: 18rpx;\n\tborder-radius: 2px;\n\ttransform: rotate(45deg);\n\tbackground-color: #fff;\n}\n.text_l::after {\n\tposition: absolute;\n\tz-index: -1;\n\tcontent: '';\n\ttop: 26rpx;\n\tleft: -8rpx;\n\twidth: 18rpx;\n\theight: 18rpx;\n\tborder-radius: 2px;\n\ttransform: rotate(45deg);\n\tbackground-color: #fff;\n}\n\n.article {\n\tbox-sizing: border-box;\n\tpadding: 14rpx 20rpx 4rpx 20rpx;\n\twidth: 490rpx;\n\tborder-radius: 10rpx;\n\toverflow: hidden;\n\tbackground-color: #fff;\n\tborder: 0.5px solid #fff;\n\t.article-info {\n\t\twidth: 100%;\n\t\t.article-img {\n\t\t\twidth: 100rpx;\n\t\t\theight: 100rpx;\n\t\t\tmargin-right: 20rpx;\n\t\t\tborder-radius: 10rpx;\n\t\t\toverflow: hidden;\n\t\t}\n\t\t.article-title {\n\t\t\tbox-sizing: border-box;\n\t\t\tmargin-bottom: 14rpx;\n\t\t}\n\t}\n\n\t.m-line {\n\t\twidth: 100%;\n\t\theight: 1px;\n\t\tmargin-top: 20rpx;\n\t}\n\t.article-b {\n\t\twidth: 100%;\n\t\tmargin-top: 4rpx;\n\t\t.article-b-icon {\n\t\t\twidth: 26rpx;\n\t\t\theight: 26rpx;\n\t\t\tbackground-color: #f1f1f1;\n\t\t\tborder-radius: 50%;\n\t\t\toverflow: hidden;\n\t\t\tmargin-right: 10rpx;\n\t\t}\n\t\t.article-b-text {\n\t\t}\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-functional-module.vue?vue&type=style&index=0&id=355e74fb&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-functional-module.vue?vue&type=style&index=0&id=355e74fb&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754970078353\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}