<template>
  <view class="mqtt-demo">
    <!-- 状态显示 -->
    <view class="status-section">
      <view class="status-card">
        <text class="status-label">连接状态:</text>
        <text :class="['status-value', isConnected ? 'connected' : 'disconnected']">
          {{ isConnected ? '已连接' : '未连接' }}
        </text>
      </view>
      <view class="status-card">
        <text class="status-label">消息数量:</text>
        <text class="status-value">{{ messages.length }}</text>
      </view>
    </view>

    <!-- 控制按钮 -->
    <view class="control-section">
      <button 
        class="control-btn connect-btn" 
        @click="connectMqtt" 
        :disabled="isConnected"
      >
        连接MQTT
      </button>
      <button 
        class="control-btn disconnect-btn" 
        @click="disconnectMqtt" 
        :disabled="!isConnected"
      >
        断开连接
      </button>
    </view>

    <!-- 消息发送 -->
    <view class="message-section">
      <view class="input-group">
        <input 
          v-model="messageInput" 
          class="message-input"
          placeholder="输入消息内容"
          @confirm="sendMessage"
        />
        <button 
          class="send-btn" 
          @click="sendMessage" 
          :disabled="!isConnected || !messageInput.trim()"
        >
          发送
        </button>
      </view>
    </view>

    <!-- 主题订阅 -->
    <view class="topic-section">
      <view class="section-title">主题订阅</view>
      <view class="input-group">
        <input 
          v-model="topicInput" 
          class="topic-input"
          placeholder="输入要订阅的主题"
        />
        <button 
          class="subscribe-btn" 
          @click="subscribeTopic" 
          :disabled="!isConnected || !topicInput.trim()"
        >
          订阅
        </button>
      </view>
      <view class="subscribed-topics">
        <view class="topic-title">已订阅主题:</view>
        <view 
          v-for="(topic, index) in subscribedTopics" 
          :key="index"
          class="topic-item"
        >
          <text class="topic-name">{{ topic }}</text>
          <button 
            class="unsubscribe-btn" 
            @click="unsubscribeTopic(topic)"
          >
            取消
          </button>
        </view>
      </view>
    </view>

    <!-- 消息列表 -->
    <view class="messages-section">
      <view class="section-title">消息列表</view>
      <scroll-view class="messages-container" scroll-y>
        <view 
          v-for="(msg, index) in messages" 
          :key="index" 
          :class="['message-item', msg.type]"
        >
          <view class="message-header">
            <text class="message-time">{{ formatTime(msg.timestamp) }}</text>
            <text class="message-topic">{{ msg.topic }}</text>
          </view>
          <view class="message-content">{{ msg.content }}</view>
        </view>
      </scroll-view>
      <button class="clear-btn" @click="clearMessages">清空消息</button>
    </view>
  </view>
</template>

<script>
// 导入MQTT工具
import mqttClient from '../../utils/mqttClient.js'
import { createUserInfo, createChatMessage, MESSAGE_TYPES, TOPIC_TEMPLATES } from '../../utils/mqttConfig.js'
import mqtt from '../../lib/mqtt.min.js'

export default {
  data() {
    return {
      isConnected: false,
      messageInput: '',
      topicInput: '',
      messages: [],
      subscribedTopics: []
    }
  },
  
  onLoad() {
    // 设置MQTT库
    mqttClient.setMqttLib(mqtt)
    
    // 检查连接状态
    this.isConnected = mqttClient.getConnectStatus()
    
    // 如果已连接，获取已订阅的主题
    if (this.isConnected) {
      this.loadSubscribedTopics()
    }
  },
  
  onUnload() {
    // 页面卸载时断开连接
    mqttClient.disconnect()
  },
  
  methods: {
    /**
     * 连接MQTT
     */
    connectMqtt() {
      // 创建用户信息
      const userInfo = createUserInfo(
        'demo-user-' + Date.now(),
        'MQTT演示用户',
        'mqtt-toolkit-demo',
        'demo-token-' + Math.random().toString(36).substr(2, 9),
        '',
        'DEV'
      )
      
      const callbacks = {
        onConnect: () => {
          console.log('MQTT连接成功')
          this.isConnected = true
          this.addMessage('系统', '连接成功', 'system')
          
          // 自动订阅演示主题
          this.autoSubscribeTopics()
        },
        
        onMessage: (topic, mqttMsg) => {
          console.log('收到消息:', topic, mqttMsg)
          this.handleMessage(topic, mqttMsg)
        },
        
        onReconnect: () => {
          console.log('MQTT重连中...')
          this.addMessage('系统', '正在重连...', 'system')
        },
        
        onError: (error) => {
          console.error('MQTT连接错误:', error)
          this.isConnected = false
          this.addMessage('系统', '连接错误: ' + error.message, 'error')
        },
        
        onEnd: () => {
          console.log('MQTT连接已断开')
          this.isConnected = false
          this.addMessage('系统', '连接已断开', 'system')
        }
      }
      
      // 连接MQTT
      try {
        mqttClient.connect(userInfo, callbacks)
      } catch (error) {
        console.error('连接失败:', error)
        uni.showToast({
          title: '连接失败: ' + error.message,
          icon: 'none'
        })
      }
    },
    
    /**
     * 断开MQTT连接
     */
    disconnectMqtt() {
      mqttClient.disconnect(false, () => {
        console.log('已断开MQTT连接')
        this.isConnected = false
        this.subscribedTopics = []
        this.addMessage('系统', '主动断开连接', 'system')
      })
    },
    
    /**
     * 发送消息
     */
    sendMessage() {
      if (!this.messageInput.trim()) {
        uni.showToast({
          title: '请输入消息内容',
          icon: 'none'
        })
        return
      }
      
      // 创建聊天消息
      const message = createChatMessage(
        this.messageInput,
        'demo-user',
        'MQTT演示用户',
        'demo-room'
      )
      
      // 发布到演示主题
      const topic = TOPIC_TEMPLATES.CHAT_ROOM('demo')
      const success = mqttClient.publish(topic, message)
      
      if (success) {
        this.addMessage(topic, this.messageInput, 'sent')
        this.messageInput = ''
        
        uni.showToast({
          title: '消息发送成功',
          icon: 'success'
        })
      } else {
        uni.showToast({
          title: '发送失败，请检查连接',
          icon: 'none'
        })
      }
    },
    
    /**
     * 订阅主题
     */
    subscribeTopic() {
      if (!this.topicInput.trim()) {
        uni.showToast({
          title: '请输入主题名称',
          icon: 'none'
        })
        return
      }
      
      const topic = this.topicInput.trim()
      
      if (this.subscribedTopics.includes(topic)) {
        uni.showToast({
          title: '该主题已订阅',
          icon: 'none'
        })
        return
      }
      
      const success = mqttClient.subscribe(topic, {}, (err) => {
        if (!err) {
          this.subscribedTopics.push(topic)
          this.addMessage('系统', `订阅主题成功: ${topic}`, 'system')
          this.topicInput = ''
          
          uni.showToast({
            title: '订阅成功',
            icon: 'success'
          })
        } else {
          console.error('订阅失败:', err)
          uni.showToast({
            title: '订阅失败',
            icon: 'none'
          })
        }
      })
      
      if (!success) {
        uni.showToast({
          title: '订阅失败，请检查连接',
          icon: 'none'
        })
      }
    },
    
    /**
     * 取消订阅主题
     */
    unsubscribeTopic(topic) {
      const success = mqttClient.unsubscribe(topic, (err) => {
        if (!err) {
          const index = this.subscribedTopics.indexOf(topic)
          if (index > -1) {
            this.subscribedTopics.splice(index, 1)
          }
          this.addMessage('系统', `取消订阅: ${topic}`, 'system')
          
          uni.showToast({
            title: '取消订阅成功',
            icon: 'success'
          })
        }
      })
      
      if (!success) {
        uni.showToast({
          title: '取消订阅失败',
          icon: 'none'
        })
      }
    },
    
    /**
     * 自动订阅演示主题
     */
    autoSubscribeTopics() {
      const demoTopics = [
        TOPIC_TEMPLATES.CHAT_ROOM('demo'),
        '/mqtt-toolkit/demo',
        '/test/topic'
      ]
      
      demoTopics.forEach(topic => {
        mqttClient.subscribe(topic, {}, (err) => {
          if (!err) {
            this.subscribedTopics.push(topic)
            this.addMessage('系统', `自动订阅: ${topic}`, 'system')
          }
        })
      })
    },
    
    /**
     * 处理接收到的消息
     */
    handleMessage(topic, mqttMsg) {
      let content = ''
      
      if (mqttMsg.command === MESSAGE_TYPES.CHAT_MSG) {
        const chatMsg = mqttMsg.data
        content = `[${chatMsg.nickname}]: ${chatMsg.content}`
      } else {
        content = JSON.stringify(mqttMsg)
      }
      
      this.addMessage(topic, content, 'received')
    },
    
    /**
     * 添加消息到列表
     */
    addMessage(topic, content, type = 'info') {
      this.messages.push({
        topic,
        content,
        type,
        timestamp: Date.now()
      })
      
      // 限制消息数量
      if (this.messages.length > 100) {
        this.messages.splice(0, this.messages.length - 100)
      }
      
      // 滚动到底部
      this.$nextTick(() => {
        // 这里可以添加滚动到底部的逻辑
      })
    },
    
    /**
     * 清空消息
     */
    clearMessages() {
      this.messages = []
      uni.showToast({
        title: '消息已清空',
        icon: 'success'
      })
    },
    
    /**
     * 加载已订阅的主题
     */
    loadSubscribedTopics() {
      // 这里可以从存储中加载已订阅的主题
      // 暂时为空实现
    },
    
    /**
     * 格式化时间
     */
    formatTime(timestamp) {
      const date = new Date(timestamp)
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
    }
  }
}
</script>

<style scoped>
.mqtt-demo {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.status-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.status-card {
  flex: 1;
  background: white;
  padding: 20rpx;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.status-value {
  font-size: 28rpx;
  font-weight: bold;
}

.connected {
  color: #4CAF50;
}

.disconnected {
  color: #f44336;
}

.control-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.control-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.connect-btn {
  background-color: #4CAF50;
  color: white;
}

.disconnect-btn {
  background-color: #f44336;
  color: white;
}

.control-btn:disabled {
  background-color: #ccc;
  color: #999;
}

.message-section, .topic-section {
  background: white;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.input-group {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.message-input, .topic-input {
  flex: 1;
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.send-btn, .subscribe-btn {
  padding: 20rpx 30rpx;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.send-btn:disabled, .subscribe-btn:disabled {
  background-color: #ccc;
}

.subscribed-topics {
  margin-top: 20rpx;
}

.topic-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.topic-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.topic-name {
  font-size: 26rpx;
  color: #333;
}

.unsubscribe-btn {
  padding: 10rpx 20rpx;
  background-color: #ff9800;
  color: white;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
}

.messages-section {
  background: white;
  padding: 20rpx;
  border-radius: 10rpx;
}

.messages-container {
  height: 400rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  padding: 10rpx;
  margin-bottom: 20rpx;
}

.message-item {
  margin-bottom: 20rpx;
  padding: 15rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #ddd;
}

.message-item.sent {
  background-color: #e3f2fd;
  border-left-color: #2196F3;
}

.message-item.received {
  background-color: #f3e5f5;
  border-left-color: #9c27b0;
}

.message-item.system {
  background-color: #fff3e0;
  border-left-color: #ff9800;
}

.message-item.error {
  background-color: #ffebee;
  border-left-color: #f44336;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.message-time {
  font-size: 24rpx;
  color: #999;
}

.message-topic {
  font-size: 24rpx;
  color: #666;
  font-weight: bold;
}

.message-content {
  font-size: 26rpx;
  color: #333;
  word-break: break-all;
}

.clear-btn {
  width: 100%;
  padding: 20rpx;
  background-color: #607d8b;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
}
</style>
