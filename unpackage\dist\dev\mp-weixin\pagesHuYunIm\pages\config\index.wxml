<view class="config-page data-v-5daee43c"><view class="config-section data-v-5daee43c"><view class="section-title data-v-5daee43c">当前MQTT配置</view><view class="config-item data-v-5daee43c"><text class="config-label data-v-5daee43c">环境:</text><picker value="{{currentEnvIndex}}" range="{{environments}}" data-event-opts="{{[['change',[['onEnvChange',['$event']]]]]}}" bindchange="__e" class="data-v-5daee43c"><view class="picker-value data-v-5daee43c">{{environments[currentEnvIndex]}}</view></picker></view><view class="config-item data-v-5daee43c"><text class="config-label data-v-5daee43c">WebSocket URL:</text><text class="config-value data-v-5daee43c">{{currentConfig.wsUrl}}</text></view><view class="config-item data-v-5daee43c"><text class="config-label data-v-5daee43c">心跳间隔:</text><text class="config-value data-v-5daee43c">{{currentConfig.pingInterval+"ms"}}</text></view><view class="config-item data-v-5daee43c"><text class="config-label data-v-5daee43c">连接超时:</text><text class="config-value data-v-5daee43c">{{currentConfig.connectTimeout+"ms"}}</text></view><view class="config-item data-v-5daee43c"><text class="config-label data-v-5daee43c">重连间隔:</text><text class="config-value data-v-5daee43c">{{currentConfig.reconnectPeriod+"ms"}}</text></view></view><view class="config-section data-v-5daee43c"><view class="section-title data-v-5daee43c">用户信息配置</view><view class="form-item data-v-5daee43c"><text class="form-label data-v-5daee43c">用户ID:</text><input class="form-input data-v-5daee43c" placeholder="请输入用户ID" data-event-opts="{{[['input',[['__set_model',['$0','userId','$event',[]],['userConfig']]]]]}}" value="{{userConfig.userId}}" bindinput="__e"/></view><view class="form-item data-v-5daee43c"><text class="form-label data-v-5daee43c">昵称:</text><input class="form-input data-v-5daee43c" placeholder="请输入昵称" data-event-opts="{{[['input',[['__set_model',['$0','nickname','$event',[]],['userConfig']]]]]}}" value="{{userConfig.nickname}}" bindinput="__e"/></view><view class="form-item data-v-5daee43c"><text class="form-label data-v-5daee43c">频道代码:</text><input class="form-input data-v-5daee43c" placeholder="请输入频道代码" data-event-opts="{{[['input',[['__set_model',['$0','channelCode','$event',[]],['userConfig']]]]]}}" value="{{userConfig.channelCode}}" bindinput="__e"/></view><view class="form-item data-v-5daee43c"><text class="form-label data-v-5daee43c">认证Token:</text><textarea class="form-textarea data-v-5daee43c" placeholder="请输入认证Token" data-event-opts="{{[['input',[['__set_model',['$0','token','$event',[]],['userConfig']]]]]}}" value="{{userConfig.token}}" bindinput="__e"></textarea></view><view class="form-item data-v-5daee43c"><text class="form-label data-v-5daee43c">头像URL:</text><input class="form-input data-v-5daee43c" placeholder="请输入头像URL（可选）" data-event-opts="{{[['input',[['__set_model',['$0','avatar','$event',[]],['userConfig']]]]]}}" value="{{userConfig.avatar}}" bindinput="__e"/></view></view><view class="config-section data-v-5daee43c"><view class="section-title data-v-5daee43c">连接选项配置</view><view class="form-item data-v-5daee43c"><text class="form-label data-v-5daee43c">心跳间隔(ms):</text><input class="form-input data-v-5daee43c" type="number" placeholder="60000" data-event-opts="{{[['input',[['__set_model',['$0','keepalive','$event',['number']],['connectionOptions']]]],['blur',[['$forceUpdate']]]]}}" value="{{connectionOptions.keepalive}}" bindinput="__e" bindblur="__e"/></view><view class="form-item data-v-5daee43c"><text class="form-label data-v-5daee43c">连接超时(ms):</text><input class="form-input data-v-5daee43c" type="number" placeholder="30000" data-event-opts="{{[['input',[['__set_model',['$0','connectTimeout','$event',['number']],['connectionOptions']]]],['blur',[['$forceUpdate']]]]}}" value="{{connectionOptions.connectTimeout}}" bindinput="__e" bindblur="__e"/></view><view class="form-item data-v-5daee43c"><text class="form-label data-v-5daee43c">重连间隔(ms):</text><input class="form-input data-v-5daee43c" type="number" placeholder="1000" data-event-opts="{{[['input',[['__set_model',['$0','reconnectPeriod','$event',['number']],['connectionOptions']]]],['blur',[['$forceUpdate']]]]}}" value="{{connectionOptions.reconnectPeriod}}" bindinput="__e" bindblur="__e"/></view><view class="form-item data-v-5daee43c"><view class="switch-item data-v-5daee43c"><text class="form-label data-v-5daee43c">清理会话:</text><switch checked="{{connectionOptions.clean}}" data-event-opts="{{[['change',[['onCleanChange',['$event']]]]]}}" bindchange="__e" class="data-v-5daee43c"></switch></view></view><view class="form-item data-v-5daee43c"><view class="switch-item data-v-5daee43c"><text class="form-label data-v-5daee43c">自动重连:</text><switch checked="{{connectionOptions.autoReconnect}}" data-event-opts="{{[['change',[['onAutoReconnectChange',['$event']]]]]}}" bindchange="__e" class="data-v-5daee43c"></switch></view></view></view><view class="config-section data-v-5daee43c"><view class="section-title data-v-5daee43c">主题模板</view><block wx:for="{{$root.l0}}" wx:for-item="template" wx:for-index="key" wx:key="key"><view class="template-item data-v-5daee43c"><view class="template-header data-v-5daee43c"><text class="template-name data-v-5daee43c">{{template.m0}}</text><button data-event-opts="{{[['tap',[['testTemplate',[key]]]]]}}" class="test-btn data-v-5daee43c" bindtap="__e">测试</button></view><text class="template-value data-v-5daee43c">{{template.$orig}}</text></view></block></view><view class="action-section data-v-5daee43c"><button data-event-opts="{{[['tap',[['saveConfig',['$event']]]]]}}" class="action-btn save-btn data-v-5daee43c" bindtap="__e">保存配置</button><button data-event-opts="{{[['tap',[['resetConfig',['$event']]]]]}}" class="action-btn reset-btn data-v-5daee43c" bindtap="__e">重置配置</button><button data-event-opts="{{[['tap',[['testConnection',['$event']]]]]}}" class="action-btn test-btn data-v-5daee43c" bindtap="__e">测试连接</button></view><view class="info-section data-v-5daee43c"><view class="section-title data-v-5daee43c">配置信息</view><view class="info-content data-v-5daee43c"><text class="info-text data-v-5daee43c">{{configInfo}}</text></view></view></view>