{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/item/m-audio.vue?648d", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/item/m-audio.vue?3136", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/item/m-audio.vue?5ab5", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/item/m-audio.vue?3749", "uni-app:///pagesGoEasy/chat_page/components/item/m-audio.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/item/m-audio.vue?eb8a", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/item/m-audio.vue?3842"], "names": ["components", "props", "isMy", "type", "default", "value", "data", "computed", "<PERSON><PERSON><PERSON><PERSON>", "width", "methods", "onClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC8L;AAC9L,gBAAgB,yLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,mBAAO,CAAC,6CAAoC;AACvE;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpBA;AAAA;AAAA;AAAA;AAA2uB,CAAgB,6qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4B/vB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;QACAC;QACAA;MACA;QACAA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAA05C,CAAgB,ouCAAG,EAAC,C;;;;;;;;;;;ACA96C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesGoEasy/chat_page/components/item/m-audio.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./m-audio.vue?vue&type=template&id=7fda2edb&scoped=true&\"\nvar renderjs\nimport script from \"./m-audio.vue?vue&type=script&lang=js&\"\nexport * from \"./m-audio.vue?vue&type=script&lang=js&\"\nimport style0 from \"./m-audio.vue?vue&type=style&index=0&id=7fda2edb&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7fda2edb\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesGoEasy/chat_page/components/item/m-audio.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-audio.vue?vue&type=template&id=7fda2edb&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.value.pause == 3 ? require(\"../../../static/chat_page/play.gif\") : null\n  var g0 = Math.ceil(_vm.value.payload.duration) || 1\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-audio.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-audio.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"flex_c\">\n\t\t<view class=\"flex_r\" :class=\"{ text_box: isMy }\">\n\t\t\t<view class=\"text-box\" @tap.stop=\"onClick\">\n\t\t\t\t<view\n\t\t\t\t\tclass=\"text_30 flex_r fa_c text\"\n\t\t\t\t\t:class=\"isMy ? 'text_r' : 'text_l'\"\n\t\t\t\t\t:style=\"{ width: setWidth }\"\n\t\t\t\t\t:hover-class=\"isMy ? 'hover_classr' : 'hover_classl'\"\n\t\t\t\t\t:hover-stay-time=\"60\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"text-icon\" v-if=\"value.pause !== 3\">\n\t\t\t\t\t\t<image\n\t\t\t\t\t\t\tclass=\"img\"\n\t\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTY3NC44MDYgNzcuMTI2YTQ2LjAwOCA0Ni4wMDggMCAwIDAtNjYuMTc2IDAgNDYuMDA4IDQ2LjAwOCAwIDAgMCAwIDYzLjAyNSA1MjAuNTg4IDUyMC41ODggMCAwIDEgMCA3MzUuNTA0IDQ3LjI2OSA0Ny4yNjkgMCAwIDAgMCA2Ni44MDcgNDcuMjY5IDQ3LjI2OSAwIDAgMCA2Ni44MDcgMCA2MTQuNDk2IDYxNC40OTYgMCAwIDAtLjYzLTg2NS4zMzZ6Ii8+PHBhdGggZD0iTTQ2Ny40NTMgMjQyLjg4MmE0Ny4yNjkgNDcuMjY5IDAgMCAwLTYzLjAyNSAwIDQ2LjYzOSA0Ni42MzkgMCAwIDAgMCA2My4wMjYgMjk5LjM3IDI5OS4zNyAwIDAgMSAwIDQyMi4yNjggNDcuMjY5IDQ3LjI2OSAwIDAgMCAwIDYzLjAyNiA0Ni42MzkgNDYuNjM5IDAgMCAwIDYzLjAyNSAwIDM5My45MDggMzkzLjkwOCAwIDAgMCAwLTU0OC4zMnptLTI3Ny4zMSAyMTQuOTE2YTc4LjE1MSA3OC4xNTEgMCAwIDAgMCAxMTAuOTI1IDc4Ljc4MiA3OC43ODIgMCAxIDAgMC0xMTAuOTI1eiIvPjwvc3ZnPg==\"\n\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"text-icon\" v-if=\"value.pause == 3\">\n\t\t\t\t\t\t<image class=\"img\" style=\"transform: scale(1.35)\"  :src=\"require('../../../static/chat_page/play.gif')\" mode=\"aspectFill\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"text-duration\">{{ Math.ceil(value.payload.duration) || 1 }}''</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n<script>\nexport default {\n\tcomponents: {},\n\tprops: {\n\t\tisMy: {\n\t\t\ttype: [Boolean, Number],\n\t\t\tdefault: false\n\t\t},\n\t\tvalue: {\n\t\t\ttype: Object,\n\t\t\tdefault: {}\n\t\t}\n\t},\n\tdata() {\n\t\treturn {};\n\t},\n\tcomputed: {\n\t\tsetWidth() {\n\t\t\tlet width = 0;\n\t\t\tconst duration = Math.ceil(this.value.payload.duration);\n\t\t\t// 基础长度\n\t\t\tlet basis = duration - 10;\n\t\t\tif (basis >= 0) {\n\t\t\t\twidth = 10 * 8 + 60;\n\t\t\t\twidth = width + basis * 2;\n\t\t\t} else {\n\t\t\t\twidth = duration * 8 + 60;\n\t\t\t}\n\t\t\treturn `${width}px`;\n\t\t}\n\t},\n\tmethods: {\n\t\tonClick() {\n\t\t\tthis.$emit('onClick');\n\t\t}\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.row_ {\n\tflex-direction: row-reverse;\n}\n.text_box {\n\tflex-direction: row-reverse;\n}\n.text-box {\n\tborder-radius: 8rpx;\n}\n\n.text {\n\tposition: relative;\n\tz-index: 99;\n\tbox-sizing: border-box;\n\tpadding: 18rpx 16rpx;\n\tborder-radius: 8rpx;\n\tword-break: break-all;\n\tvertical-align: center;\n\t.text-icon {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t\tmargin: 0 4rpx;\n\t}\n\t.text-duration {\n\t\tmargin: 0 8rpx;\n\t}\n}\n\n.text_r {\n\tposition: relative;\n\tz-index: 2;\n\tbackground-color: #95ec6a;\n\tflex-direction: row-reverse;\n\t.text-icon {\n\t\ttransform: rotate(180deg);\n\t}\n}\n.text_l {\n\tposition: relative;\n\tz-index: 2;\n\tbackground-color: #fff;\n}\n\n.text_r::after {\n\tposition: absolute;\n\tz-index: -1;\n\tcontent: '';\n\ttop: 28rpx;\n\tright: -8rpx;\n\twidth: 18rpx;\n\theight: 18rpx;\n\tborder-radius: 2px;\n\ttransform: rotate(45deg);\n\tbackground-color: #95ec6a;\n}\n.text_l::after {\n\tposition: absolute;\n\tz-index: -1;\n\tcontent: '';\n\ttop: 28rpx;\n\tleft: -8rpx;\n\twidth: 18rpx;\n\theight: 18rpx;\n\tborder-radius: 2px;\n\ttransform: rotate(45deg);\n\tbackground-color: #fff;\n}\n.hover_classr {\n\tbackground-color: #89d961;\n}\n.hover_classl {\n\tbackground-color: #e2e2e2;\n}\n\n.hover_classr::after {\n\tposition: absolute;\n\tz-index: -1;\n\tcontent: '';\n\ttop: 28rpx;\n\tright: -8rpx;\n\twidth: 18rpx;\n\theight: 18rpx;\n\tborder-radius: 2px;\n\ttransform: rotate(45deg);\n\tbackground-color: #89d961;\n}\n.hover_classl::after {\n\tposition: absolute;\n\tz-index: -1;\n\tcontent: '';\n\ttop: 28rpx;\n\tleft: -8rpx;\n\twidth: 18rpx;\n\theight: 18rpx;\n\tborder-radius: 2px;\n\ttransform: rotate(45deg);\n\tbackground-color: #e2e2e2;\n}\n</style>\n", "import mod from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-audio.vue?vue&type=style&index=0&id=7fda2edb&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-audio.vue?vue&type=style&index=0&id=7fda2edb&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754970078285\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}