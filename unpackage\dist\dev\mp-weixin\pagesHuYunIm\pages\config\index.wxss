
.config-page.data-v-5daee43c {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.config-section.data-v-5daee43c,
.info-section.data-v-5daee43c {
  background: white;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  padding: 20rpx;
}
.section-title.data-v-5daee43c {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 10rpx;
}
.config-item.data-v-5daee43c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.config-label.data-v-5daee43c {
  font-size: 28rpx;
  color: #666;
  width: 200rpx;
}
.config-value.data-v-5daee43c {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  text-align: right;
}
.picker-value.data-v-5daee43c {
  font-size: 26rpx;
  color: #007aff;
  padding: 10rpx;
  border: 1rpx solid #ddd;
  border-radius: 6rpx;
}
.form-item.data-v-5daee43c {
  margin-bottom: 20rpx;
}
.form-label.data-v-5daee43c {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}
.form-input.data-v-5daee43c {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 26rpx;
  box-sizing: border-box;
}
.form-textarea.data-v-5daee43c {
  width: 100%;
  height: 120rpx;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 26rpx;
  box-sizing: border-box;
  resize: none;
}
.switch-item.data-v-5daee43c {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.template-item.data-v-5daee43c {
  margin-bottom: 20rpx;
  padding: 15rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}
.template-header.data-v-5daee43c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}
.template-name.data-v-5daee43c {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.template-value.data-v-5daee43c {
  font-size: 24rpx;
  color: #666;
  font-family: monospace;
  word-break: break-all;
}
.test-btn.data-v-5daee43c {
  padding: 8rpx 16rpx;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
}
.action-section.data-v-5daee43c {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}
.action-btn.data-v-5daee43c {
  flex: 1;
  padding: 25rpx;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: white;
}
.save-btn.data-v-5daee43c {
  background-color: #4caf50;
}
.reset-btn.data-v-5daee43c {
  background-color: #f44336;
}
.test-btn.data-v-5daee43c {
  background-color: #2196f3;
}
.info-content.data-v-5daee43c {
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 8rpx;
}
.info-text.data-v-5daee43c {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  white-space: pre-line;
  font-family: monospace;
}

