
.toolkit-home.data-v-6441f5a4 {
	padding: 20rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
}
.header-section.data-v-6441f5a4 {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 30rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}
.toolkit-logo.data-v-6441f5a4 {
	width: 120rpx;
	height: 120rpx;
	border-radius: 20rpx;
	margin-right: 30rpx;
}
.header-content.data-v-6441f5a4 {
	flex: 1;
}
.toolkit-title.data-v-6441f5a4 {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}
.toolkit-subtitle.data-v-6441f5a4 {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}
.toolkit-description.data-v-6441f5a4 {
	font-size: 24rpx;
	color: #999;
	line-height: 1.5;
	display: block;
}
.features-section.data-v-6441f5a4,
.quick-access-section.data-v-6441f5a4,
.status-section.data-v-6441f5a4,
.usage-section.data-v-6441f5a4 {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}
.section-title.data-v-6441f5a4 {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
	text-align: center;
}
.features-grid.data-v-6441f5a4 {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}
.feature-item.data-v-6441f5a4 {
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 25rpx;
	text-align: center;
	border: 2rpx solid #e9ecef;
}
.feature-icon.data-v-6441f5a4 {
	font-size: 40rpx;
	margin-bottom: 15rpx;
}
.feature-title.data-v-6441f5a4 {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}
.feature-desc.data-v-6441f5a4 {
	font-size: 24rpx;
	color: #666;
	display: block;
}
.access-buttons.data-v-6441f5a4 {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}
.access-btn.data-v-6441f5a4 {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-radius: 15rpx;
	border: none;
	text-align: left;
	box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);
}
.demo-btn.data-v-6441f5a4 {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.config-btn.data-v-6441f5a4 {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	color: white;
}
.btn-icon.data-v-6441f5a4 {
	font-size: 50rpx;
	margin-right: 25rpx;
}
.btn-content.data-v-6441f5a4 {
	flex: 1;
}
.btn-title.data-v-6441f5a4 {
	font-size: 32rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}
.btn-desc.data-v-6441f5a4 {
	font-size: 26rpx;
	opacity: 0.9;
	display: block;
}
.status-cards.data-v-6441f5a4 {
	display: flex;
	gap: 20rpx;
}
.status-card.data-v-6441f5a4 {
	flex: 1;
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 25rpx;
	text-align: center;
	border: 2rpx solid #e9ecef;
}
.status-label.data-v-6441f5a4 {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}
.status-value.data-v-6441f5a4 {
	font-size: 28rpx;
	font-weight: bold;
	display: block;
}
.connected.data-v-6441f5a4 {
	color: #28a745;
}
.disconnected.data-v-6441f5a4 {
	color: #dc3545;
}
.usage-steps.data-v-6441f5a4 {
	display: flex;
	flex-direction: column;
	gap: 25rpx;
}
.usage-step.data-v-6441f5a4 {
	display: flex;
	align-items: center;
}
.step-number.data-v-6441f5a4 {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: bold;
	margin-right: 25rpx;
}
.step-content.data-v-6441f5a4 {
	flex: 1;
}
.step-title.data-v-6441f5a4 {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}
.step-desc.data-v-6441f5a4 {
	font-size: 24rpx;
	color: #666;
	display: block;
}
.footer-section.data-v-6441f5a4 {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	text-align: center;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}
.footer-text.data-v-6441f5a4 {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}
.footer-version.data-v-6441f5a4 {
	font-size: 24rpx;
	color: #666;
	display: block;
}

