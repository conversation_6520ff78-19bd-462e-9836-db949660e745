<template>
	<view class="toolkit-home">
		<!-- 头部介绍 -->
		<view class="header-section">
			<image class="toolkit-logo" src="/static/logo.png"></image>
			<view class="header-content">
				<text class="toolkit-title">MQTT Toolkit</text>
				<text class="toolkit-subtitle">专业的MQTT工具包</text>
				<text class="toolkit-description">提供完整的MQTT连接、消息处理、配置管理功能</text>
			</view>
		</view>

		<!-- 功能特性 -->
		<view class="features-section">
			<view class="section-title">核心特性</view>
			<view class="features-grid">
				<view class="feature-item">
					<view class="feature-icon">🔄</view>
					<text class="feature-title">自动重连</text>
					<text class="feature-desc">智能重连机制</text>
				</view>
				<view class="feature-item">
					<view class="feature-icon">💓</view>
					<text class="feature-title">心跳保活</text>
					<text class="feature-desc">自动心跳维持</text>
				</view>
				<view class="feature-item">
					<view class="feature-icon">📦</view>
					<text class="feature-title">单例模式</text>
					<text class="feature-desc">全局共享连接</text>
				</view>
				<view class="feature-item">
					<view class="feature-icon">⚙️</view>
					<text class="feature-title">配置管理</text>
					<text class="feature-desc">可视化配置</text>
				</view>
				<view class="feature-item">
					<view class="feature-icon">🎯</view>
					<text class="feature-title">主题模板</text>
					<text class="feature-desc">预定义模板</text>
				</view>
				<view class="feature-item">
					<view class="feature-icon">🛡️</view>
					<text class="feature-title">错误处理</text>
					<text class="feature-desc">完善异常处理</text>
				</view>
			</view>
		</view>

		<!-- 快速入口 -->
		<view class="quick-access-section">
			<view class="section-title">快速入口</view>
			<view class="access-buttons">
				<button class="access-btn demo-btn" @click="goToDemo">
					<view class="btn-icon">🚀</view>
					<view class="btn-content">
						<text class="btn-title">功能演示</text>
						<text class="btn-desc">体验完整MQTT功能</text>
					</view>
				</button>

				<button class="access-btn config-btn" @click="goToConfig">
					<view class="btn-icon">⚙️</view>
					<view class="btn-content">
						<text class="btn-title">配置管理</text>
						<text class="btn-desc">管理MQTT连接配置</text>
					</view>
				</button>
			</view>
		</view>

		<!-- 状态信息 -->
		<view class="status-section">
			<view class="section-title">当前状态</view>
			<view class="status-cards">
				<view class="status-card">
					<text class="status-label">连接状态</text>
					<text :class="['status-value', isConnected ? 'connected' : 'disconnected']">
						{{ isConnected ? '已连接' : '未连接' }}
					</text>
				</view>
				<view class="status-card">
					<text class="status-label">工具版本</text>
					<text class="status-value">v1.0.0</text>
				</view>
			</view>
		</view>

		<!-- 使用说明 -->
		<view class="usage-section">
			<view class="section-title">使用说明</view>
			<view class="usage-steps">
				<view class="usage-step">
					<view class="step-number">1</view>
					<view class="step-content">
						<text class="step-title">配置连接</text>
						<text class="step-desc">在配置页面设置MQTT服务器信息</text>
					</view>
				</view>
				<view class="usage-step">
					<view class="step-number">2</view>
					<view class="step-content">
						<text class="step-title">测试连接</text>
						<text class="step-desc">使用演示页面测试MQTT功能</text>
					</view>
				</view>
				<view class="usage-step">
					<view class="step-number">3</view>
					<view class="step-content">
						<text class="step-title">集成使用</text>
						<text class="step-desc">在项目中导入并使用MQTT工具</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部信息 -->
		<view class="footer-section">
			<text class="footer-text">MQTT Toolkit - 让MQTT使用更简单</text>
			<text class="footer-version">Version 1.0.0</text>
		</view>
	</view>
</template>

<script>
import mqttClient from '../../utils/mqttClient.js'

export default {
	data() {
		return {
			isConnected: false
		}
	},

	onLoad() {
		// 检查MQTT连接状态
		this.checkConnectionStatus()
	},

	onShow() {
		// 页面显示时更新状态
		this.checkConnectionStatus()
	},

	methods: {
		/**
		 * 检查连接状态
		 */
		checkConnectionStatus() {
			this.isConnected = mqttClient.getConnectStatus()
		},

		/**
		 * 跳转到演示页面
		 */
		goToDemo() {
			uni.navigateTo({
				url: '/mqtt-toolkit/pages/demo/index'
			})
		},

		/**
		 * 跳转到配置页面
		 */
		goToConfig() {
			uni.navigateTo({
				url: '/mqtt-toolkit/pages/config/index'
			})
		}
	}
}
</script>

<style scoped>
.toolkit-home {
	padding: 20rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
}

.header-section {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 30rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.toolkit-logo {
	width: 120rpx;
	height: 120rpx;
	border-radius: 20rpx;
	margin-right: 30rpx;
}

.header-content {
	flex: 1;
}

.toolkit-title {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.toolkit-subtitle {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.toolkit-description {
	font-size: 24rpx;
	color: #999;
	line-height: 1.5;
	display: block;
}

.features-section,
.quick-access-section,
.status-section,
.usage-section {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
	text-align: center;
}

.features-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.feature-item {
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 25rpx;
	text-align: center;
	border: 2rpx solid #e9ecef;
}

.feature-icon {
	font-size: 40rpx;
	margin-bottom: 15rpx;
}

.feature-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.feature-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.access-buttons {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.access-btn {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-radius: 15rpx;
	border: none;
	text-align: left;
	box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);
}

.demo-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.config-btn {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	color: white;
}

.btn-icon {
	font-size: 50rpx;
	margin-right: 25rpx;
}

.btn-content {
	flex: 1;
}

.btn-title {
	font-size: 32rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.btn-desc {
	font-size: 26rpx;
	opacity: 0.9;
	display: block;
}

.status-cards {
	display: flex;
	gap: 20rpx;
}

.status-card {
	flex: 1;
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 25rpx;
	text-align: center;
	border: 2rpx solid #e9ecef;
}

.status-label {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.status-value {
	font-size: 28rpx;
	font-weight: bold;
	display: block;
}

.connected {
	color: #28a745;
}

.disconnected {
	color: #dc3545;
}

.usage-steps {
	display: flex;
	flex-direction: column;
	gap: 25rpx;
}

.usage-step {
	display: flex;
	align-items: center;
}

.step-number {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: bold;
	margin-right: 25rpx;
}

.step-content {
	flex: 1;
}

.step-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.step-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.footer-section {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	text-align: center;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.footer-text {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.footer-version {
	font-size: 24rpx;
	color: #666;
	display: block;
}
</style>
