<view class="mqtt-demo data-v-e9e45546"><view class="status-section data-v-e9e45546"><view class="status-card data-v-e9e45546"><text class="status-label data-v-e9e45546">连接状态:</text><text class="{{['data-v-e9e45546','status-value',isConnected?'connected':'disconnected']}}">{{''+(isConnected?'已连接':'未连接')+''}}</text></view><view class="status-card data-v-e9e45546"><text class="status-label data-v-e9e45546">消息数量:</text><text class="status-value data-v-e9e45546">{{$root.g0}}</text></view></view><view class="control-section data-v-e9e45546"><button class="control-btn connect-btn data-v-e9e45546" disabled="{{isConnected}}" data-event-opts="{{[['tap',[['connectMqtt',['$event']]]]]}}" bindtap="__e">连接MQTT</button><button class="control-btn disconnect-btn data-v-e9e45546" disabled="{{!isConnected}}" data-event-opts="{{[['tap',[['disconnectMqtt',['$event']]]]]}}" bindtap="__e">断开连接</button></view><view class="message-section data-v-e9e45546"><view class="input-group data-v-e9e45546"><input class="message-input data-v-e9e45546" placeholder="输入消息内容" data-event-opts="{{[['confirm',[['sendMessage',['$event']]]],['input',[['__set_model',['','messageInput','$event',[]]]]]]}}" value="{{messageInput}}" bindconfirm="__e" bindinput="__e"/><button class="send-btn data-v-e9e45546" disabled="{{$root.g1}}" data-event-opts="{{[['tap',[['sendMessage',['$event']]]]]}}" bindtap="__e">发送</button></view></view><view class="topic-section data-v-e9e45546"><view class="section-title data-v-e9e45546">主题订阅</view><view class="input-group data-v-e9e45546"><input class="topic-input data-v-e9e45546" placeholder="输入要订阅的主题" data-event-opts="{{[['input',[['__set_model',['','topicInput','$event',[]]]]]]}}" value="{{topicInput}}" bindinput="__e"/><button class="subscribe-btn data-v-e9e45546" disabled="{{$root.g2}}" data-event-opts="{{[['tap',[['subscribeTopic',['$event']]]]]}}" bindtap="__e">订阅</button></view><view class="subscribed-topics data-v-e9e45546"><view class="topic-title data-v-e9e45546">已订阅主题:</view><block wx:for="{{subscribedTopics}}" wx:for-item="topic" wx:for-index="index" wx:key="index"><view class="topic-item data-v-e9e45546"><text class="topic-name data-v-e9e45546">{{topic}}</text><button data-event-opts="{{[['tap',[['unsubscribeTopic',['$0'],[[['subscribedTopics','',index]]]]]]]}}" class="unsubscribe-btn data-v-e9e45546" bindtap="__e">取消</button></view></block></view></view><view class="messages-section data-v-e9e45546"><view class="section-title data-v-e9e45546">消息列表</view><scroll-view class="messages-container data-v-e9e45546" scroll-y="{{true}}"><block wx:for="{{$root.l0}}" wx:for-item="msg" wx:for-index="index" wx:key="index"><view class="{{['data-v-e9e45546','message-item',msg.$orig.type]}}"><view class="message-header data-v-e9e45546"><text class="message-time data-v-e9e45546">{{msg.m0}}</text><text class="message-topic data-v-e9e45546">{{msg.$orig.topic}}</text></view><view class="message-content data-v-e9e45546">{{msg.$orig.content}}</view></view></block></scroll-view><button data-event-opts="{{[['tap',[['clearMessages',['$event']]]]]}}" class="clear-btn data-v-e9e45546" bindtap="__e">清空消息</button></view></view>