<template>
  <view class="config-page">
    <!-- 当前配置显示 -->
    <view class="config-section">
      <view class="section-title">当前MQTT配置</view>

      <view class="config-item">
        <text class="config-label">环境:</text>
        <picker :value="currentEnvIndex" :range="environments" @change="onEnvChange">
          <view class="picker-value">{{ environments[currentEnvIndex] }}</view>
        </picker>
      </view>

      <view class="config-item">
        <text class="config-label">WebSocket URL:</text>
        <text class="config-value">{{ currentConfig.wsUrl }}</text>
      </view>

      <view class="config-item">
        <text class="config-label">心跳间隔:</text>
        <text class="config-value">{{ currentConfig.pingInterval }}ms</text>
      </view>

      <view class="config-item">
        <text class="config-label">连接超时:</text>
        <text class="config-value">{{ currentConfig.connectTimeout }}ms</text>
      </view>

      <view class="config-item">
        <text class="config-label">重连间隔:</text>
        <text class="config-value">{{ currentConfig.reconnectPeriod }}ms</text>
      </view>
    </view>

    <!-- 用户信息配置 -->
    <view class="config-section">
      <view class="section-title">用户信息配置</view>

      <view class="form-item">
        <text class="form-label">用户ID:</text>
        <input v-model="userConfig.userId" class="form-input" placeholder="请输入用户ID" />
      </view>

      <view class="form-item">
        <text class="form-label">昵称:</text>
        <input v-model="userConfig.nickname" class="form-input" placeholder="请输入昵称" />
      </view>

      <view class="form-item">
        <text class="form-label">频道代码:</text>
        <input v-model="userConfig.channelCode" class="form-input" placeholder="请输入频道代码" />
      </view>

      <view class="form-item">
        <text class="form-label">认证Token:</text>
        <textarea v-model="userConfig.token" class="form-textarea" placeholder="请输入认证Token" />
      </view>

      <view class="form-item">
        <text class="form-label">头像URL:</text>
        <input v-model="userConfig.avatar" class="form-input" placeholder="请输入头像URL（可选）" />
      </view>
    </view>

    <!-- 连接选项配置 -->
    <view class="config-section">
      <view class="section-title">连接选项配置</view>

      <view class="form-item">
        <text class="form-label">心跳间隔(ms):</text>
        <input v-model.number="connectionOptions.keepalive" class="form-input" type="number" placeholder="60000" />
      </view>

      <view class="form-item">
        <text class="form-label">连接超时(ms):</text>
        <input v-model.number="connectionOptions.connectTimeout" class="form-input" type="number" placeholder="30000" />
      </view>

      <view class="form-item">
        <text class="form-label">重连间隔(ms):</text>
        <input v-model.number="connectionOptions.reconnectPeriod" class="form-input" type="number" placeholder="1000" />
      </view>

      <view class="form-item">
        <view class="switch-item">
          <text class="form-label">清理会话:</text>
          <switch :checked="connectionOptions.clean" @change="onCleanChange" />
        </view>
      </view>

      <view class="form-item">
        <view class="switch-item">
          <text class="form-label">自动重连:</text>
          <switch :checked="connectionOptions.autoReconnect" @change="onAutoReconnectChange" />
        </view>
      </view>
    </view>

    <!-- 主题模板 -->
    <view class="config-section">
      <view class="section-title">主题模板</view>

      <view class="template-item" v-for="(template, key) in topicTemplates" :key="key">
        <view class="template-header">
          <text class="template-name">{{ getTemplateName(key) }}</text>
          <button class="test-btn" @click="testTemplate(key)">测试</button>
        </view>
        <text class="template-value">{{ template }}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="action-btn save-btn" @click="saveConfig">保存配置</button>
      <button class="action-btn reset-btn" @click="resetConfig">重置配置</button>
      <button class="action-btn test-btn" @click="testConnection">测试连接</button>
    </view>

    <!-- 配置信息展示 -->
    <view class="info-section">
      <view class="section-title">配置信息</view>
      <view class="info-content">
        <text class="info-text">{{ configInfo }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { MQTT_CONFIG, TOPIC_TEMPLATES, getWsUrl, createUserInfo } from '../../utils/mqttConfig.js'
import mqttClient from '../../utils/mqttClient.js'
import mqtt from '../../lib/mqtt.min.js'

export default {
  data() {
    return {
      environments: ['DEV', 'TEST', 'PROD'],
      currentEnvIndex: 0,

      userConfig: {
        userId: 'config-user-' + Date.now(),
        nickname: '配置测试用户',
        channelCode: 'config-channel',
        token: 'config-test-token',
        avatar: ''
      },

      connectionOptions: {
        keepalive: 60000,
        connectTimeout: 30000,
        reconnectPeriod: 1000,
        clean: true,
        autoReconnect: true
      },

      topicTemplates: {},
      configInfo: ''
    }
  },

  computed: {
    currentConfig() {
      const env = this.environments[this.currentEnvIndex]
      return {
        wsUrl: getWsUrl(env),
        pingInterval: MQTT_CONFIG.PING.INTERVAL,
        connectTimeout: MQTT_CONFIG.DEFAULT_OPTIONS.connectTimeout,
        reconnectPeriod: MQTT_CONFIG.DEFAULT_OPTIONS.reconnectPeriod
      }
    }
  },

  onLoad() {
    this.loadConfig()
    this.loadTopicTemplates()
    this.updateConfigInfo()

    // 设置MQTT库
    mqttClient.setMqttLib(mqtt)
  },

  methods: {
    /**
     * 加载配置
     */
    loadConfig() {
      try {
        const savedConfig = uni.getStorageSync('mqtt-toolkit-config')
        if (savedConfig) {
          this.userConfig = { ...this.userConfig, ...savedConfig.userConfig }
          this.connectionOptions = { ...this.connectionOptions, ...savedConfig.connectionOptions }
          this.currentEnvIndex = savedConfig.currentEnvIndex || 0
        }
      } catch (error) {
        console.error('加载配置失败:', error)
      }
    },

    /**
     * 加载主题模板
     */
    loadTopicTemplates() {
      this.topicTemplates = {
        USER_CHANNEL: TOPIC_TEMPLATES.USER_CHANNEL('用户ID'),
        PING: TOPIC_TEMPLATES.PING('用户ID'),
        CHAT_ROOM: TOPIC_TEMPLATES.CHAT_ROOM('房间ID'),
        PRIVATE_CHAT: TOPIC_TEMPLATES.PRIVATE_CHAT('发送者ID', '接收者ID'),
        GROUP_CHAT: TOPIC_TEMPLATES.GROUP_CHAT('群组ID'),
        SYSTEM_NOTIFY: TOPIC_TEMPLATES.SYSTEM_NOTIFY('用户ID'),
        ONLINE_STATUS: TOPIC_TEMPLATES.ONLINE_STATUS('用户ID')
      }
    },

    /**
     * 环境变化
     */
    onEnvChange(e) {
      this.currentEnvIndex = e.detail.value
      this.updateConfigInfo()
    },

    /**
     * 清理会话开关变化
     */
    onCleanChange(e) {
      this.connectionOptions.clean = e.detail.value
    },

    /**
     * 自动重连开关变化
     */
    onAutoReconnectChange(e) {
      this.connectionOptions.autoReconnect = e.detail.value
    },

    /**
     * 获取模板名称
     */
    getTemplateName(key) {
      const names = {
        USER_CHANNEL: '用户频道',
        PING: '心跳主题',
        CHAT_ROOM: '聊天室',
        PRIVATE_CHAT: '私聊',
        GROUP_CHAT: '群聊',
        SYSTEM_NOTIFY: '系统通知',
        ONLINE_STATUS: '在线状态'
      }
      return names[key] || key
    },

    /**
     * 测试主题模板
     */
    testTemplate(key) {
      const template = this.topicTemplates[key]
      uni.showModal({
        title: '主题模板',
        content: `${this.getTemplateName(key)}: ${template}`,
        showCancel: false
      })
    },

    /**
     * 保存配置
     */
    saveConfig() {
      try {
        const config = {
          userConfig: this.userConfig,
          connectionOptions: this.connectionOptions,
          currentEnvIndex: this.currentEnvIndex
        }

        uni.setStorageSync('mqtt-toolkit-config', config)

        uni.showToast({
          title: '配置保存成功',
          icon: 'success'
        })

        this.updateConfigInfo()
      } catch (error) {
        console.error('保存配置失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        })
      }
    },

    /**
     * 重置配置
     */
    resetConfig() {
      uni.showModal({
        title: '确认重置',
        content: '确定要重置所有配置吗？',
        success: (res) => {
          if (res.confirm) {
            this.userConfig = {
              userId: 'config-user-' + Date.now(),
              nickname: '配置测试用户',
              channelCode: 'config-channel',
              token: 'config-test-token',
              avatar: ''
            }

            this.connectionOptions = {
              keepalive: 60000,
              connectTimeout: 30000,
              reconnectPeriod: 1000,
              clean: true,
              autoReconnect: true
            }

            this.currentEnvIndex = 0

            try {
              uni.removeStorageSync('mqtt-toolkit-config')
              uni.showToast({
                title: '配置已重置',
                icon: 'success'
              })
              this.updateConfigInfo()
            } catch (error) {
              console.error('重置配置失败:', error)
            }
          }
        }
      })
    },

    /**
     * 测试连接
     */
    testConnection() {
      if (!this.userConfig.userId || !this.userConfig.channelCode || !this.userConfig.token) {
        uni.showToast({
          title: '请填写完整的用户信息',
          icon: 'none'
        })
        return
      }

      uni.showLoading({
        title: '测试连接中...'
      })

      try {
        const env = this.environments[this.currentEnvIndex]
        const userInfo = createUserInfo(
          this.userConfig.userId,
          this.userConfig.nickname,
          this.userConfig.channelCode,
          this.userConfig.token,
          this.userConfig.avatar,
          env
        )

        const testClient = mqttClient

        testClient.connect(
          userInfo,
          {
            onConnect: () => {
              uni.hideLoading()
              uni.showToast({
                title: '连接测试成功',
                icon: 'success'
              })

              // 断开测试连接
              setTimeout(() => {
                testClient.disconnect()
              }, 1000)
            },

            onError: (error) => {
              uni.hideLoading()
              uni.showModal({
                title: '连接测试失败',
                content: error.message,
                showCancel: false
              })
            }
          },
          this.connectionOptions
        )
      } catch (error) {
        uni.hideLoading()
        uni.showModal({
          title: '连接测试失败',
          content: error.message,
          showCancel: false
        })
      }
    },

    /**
     * 更新配置信息
     */
    updateConfigInfo() {
      const env = this.environments[this.currentEnvIndex]
      const info = `
当前环境: ${env}
WebSocket URL: ${this.currentConfig.wsUrl}
用户ID: ${this.userConfig.userId}
频道代码: ${this.userConfig.channelCode}
心跳间隔: ${this.connectionOptions.keepalive}ms
连接超时: ${this.connectionOptions.connectTimeout}ms
重连间隔: ${this.connectionOptions.reconnectPeriod}ms
清理会话: ${this.connectionOptions.clean ? '是' : '否'}
自动重连: ${this.connectionOptions.autoReconnect ? '是' : '否'}
      `.trim()

      this.configInfo = info
    }
  }
}
</script>

<style scoped>
.config-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.config-section,
.info-section {
  background: white;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  padding: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 10rpx;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.config-label {
  font-size: 28rpx;
  color: #666;
  width: 200rpx;
}

.config-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

.picker-value {
  font-size: 26rpx;
  color: #007aff;
  padding: 10rpx;
  border: 1rpx solid #ddd;
  border-radius: 6rpx;
}

.form-item {
  margin-bottom: 20rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 26rpx;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 120rpx;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 26rpx;
  box-sizing: border-box;
  resize: none;
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-item {
  margin-bottom: 20rpx;
  padding: 15rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.template-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.template-value {
  font-size: 24rpx;
  color: #666;
  font-family: monospace;
  word-break: break-all;
}

.test-btn {
  padding: 8rpx 16rpx;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
}

.action-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 25rpx;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: white;
}

.save-btn {
  background-color: #4caf50;
}

.reset-btn {
  background-color: #f44336;
}

.test-btn {
  background-color: #2196f3;
}

.info-content {
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 8rpx;
}

.info-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  white-space: pre-line;
  font-family: monospace;
}
</style>
